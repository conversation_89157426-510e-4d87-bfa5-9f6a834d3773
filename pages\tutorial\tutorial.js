Page({
  /**
   * 页面的初始数据
   */
  data: {
    topPadding: 0, // 顶部安全区域高度
    currentPlayingId: null, // 当前播放的教程ID
    isPlaying: false, // 是否正在播放
    videoProgress: 0, // 播放进度百分比
    currentTime: '00:00', // 当前播放时间
    tutorialList: [
      {
        id: 1,
        title: '发布求购教程',
        description: '学习如何发布求购信息，快速找到心仪的苗木',
        duration: '3:45',
        category: 'publish',
        icon: 'shop',
        videoUrl: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/vedios/123.mp4', // 测试视频
        thumbnail: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/images/tutorial-thumbnails/publish-demand.jpg' // 发布求购教程缩略图
      },
      {
        id: 2,
        title: '发布供应教程',
        description: '了解如何发布供应信息，让更多买家找到您',
        duration: '4:20',
        category: 'publish',
        icon: 'store',
        videoUrl: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/vedios/123.mp4', // 测试视频
        thumbnail: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/images/tutorial-thumbnails/publish-supply.jpg' // 发布供应教程缩略图
      },
      {
        id: 3,
        title: '平台使用入门',
        description: '新手必看，快速了解平台基本功能和操作',
        duration: '5:15',
        category: 'usage',
        icon: 'help',
        videoUrl: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/vedios/123.mp4', // 测试视频
        thumbnail: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/images/tutorial-thumbnails/platform-intro.jpg' // 平台使用入门缩略图
      },
      {
        id: 4,
        title: '搜索筛选技巧',
        description: '掌握高效搜索方法，快速找到目标信息',
        duration: '2:30',
        category: 'usage',
        icon: 'search',
        videoUrl: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/vedios/123.mp4', // 测试视频
        thumbnail: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/images/tutorial-thumbnails/search-tips.jpg' // 搜索筛选技巧缩略图
      },
      {
        id: 5,
        title: '收藏管理教程',
        description: '学会管理收藏夹，轻松整理感兴趣的内容',
        duration: '3:10',
        category: 'usage',
        icon: 'heart',
        videoUrl: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/vedios/123.mp4', // 测试视频
        thumbnail: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/images/tutorial-thumbnails/favorites-management.jpg' // 收藏管理教程缩略图
      },
      {
        id: 6,
        title: '联系沟通指南',
        description: '了解如何与买家卖家有效沟通，提高成交率',
        duration: '4:05',
        category: 'usage',
        icon: 'chat',
        videoUrl: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/vedios/123.mp4', // 测试视频
        thumbnail: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/images/tutorial-thumbnails/communication-guide.jpg' // 联系沟通指南缩略图
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '视频教程'
    });

    // 获取系统信息，计算顶部安全区域
    this.getSystemInfo();
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const navBarHeight = 44; // 导航栏高度
    const topPadding = statusBarHeight + navBarHeight;

    this.setData({
      topPadding: topPadding
    });
  },

  /**
   * 视频播放事件
   */
  onVideoPlay(e) {
    const tutorialId = e.currentTarget.dataset.id;
    this.setData({
      currentPlayingId: tutorialId,
      isPlaying: true
    });
    console.log(`教程 ${tutorialId} 开始播放`);
  },

  /**
   * 视频暂停事件
   */
  onVideoPause(e) {
    const tutorialId = e.currentTarget.dataset.id;
    this.setData({
      isPlaying: false
    });
    console.log(`教程 ${tutorialId} 暂停播放`);
  },

  /**
   * 视频播放结束事件
   */
  onVideoEnded(e) {
    const tutorialId = e.currentTarget.dataset.id;
    this.setData({
      currentPlayingId: null,
      isPlaying: false,
      videoProgress: 100
    });
    console.log(`教程 ${tutorialId} 播放结束`);
  },

  /**
   * 视频播放错误事件
   */
  onVideoError(e) {
    const tutorialId = e.currentTarget.dataset.id;
    console.error(`教程 ${tutorialId} 视频播放错误:`, e.detail);
    wx.showToast({
      title: '视频播放失败',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 视频时间更新事件
   */
  onVideoTimeUpdate(e) {
    const tutorialId = e.currentTarget.dataset.id;
    const { currentTime, duration } = e.detail;
    const progress = duration > 0 ? Math.round((currentTime / duration) * 100) : 0;
    const formatTime = this.formatTime(currentTime);

    this.setData({
      videoProgress: progress,
      currentTime: formatTime
    });
  },

  /**
   * 格式化时间
   */
  formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },



  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉刷新
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
});
