<!-- pages/demand/demand.wxml -->
<page-meta disable-scroll="{{showFilter}}">
  <navigation-bar title="求购大厅" back="{{false}}" homeButton="{{true}}" extClass="custom-nav" hideTree="{{true}}"></navigation-bar>
</page-meta>

<!-- 防遮挡占位元素 -->
<view class="nav-placeholder" style="height:{{topPadding}}px;"></view>

<!-- 筛选面板 - 移到最外层 -->
<view class="filter-panel {{showFilter ? 'show' : ''}}" catch:touchmove="{{showFilter ? 'preventTouchMove' : ''}}">
  <view class="filter-content">
    <view class="filter-title">筛选条件</view>

    <!-- 质量筛选条件 -->
    <view class="filter-section">
      <view class="filter-section-title">
        <text>质量要求</text>
        <view class="filter-clear-btn" wx:if="{{filters.quality !== 'all'}}" bindtap="clearQuality">
          <t-icon name="close" size="16" color="#e53935"></t-icon>
          <text>清除</text>
        </view>
      </view>

      <view class="quality-options">
        <view class="quality-option {{filters.quality === 'all' ? 'active' : ''}}" bindtap="setQuality" data-value="all">全部</view>
        <view class="quality-option {{filters.quality === '精品' ? 'active' : ''}}" bindtap="setQuality" data-value="精品">精品</view>
        <view class="quality-option {{filters.quality === '中等' ? 'active' : ''}}" bindtap="setQuality" data-value="中等">中等</view>
        <view class="quality-option {{filters.quality === '处理' ? 'active' : ''}}" bindtap="setQuality" data-value="处理">处理</view>
      </view>
    </view>

    <!-- 采购地点筛选条件 -->
    <view class="filter-section">
      <view class="filter-section-title">
        <text>买家采购范围</text>
        <view class="filter-clear-btn" wx:if="{{filters.province !== 'all'}}" bindtap="clearProvince">
          <t-icon name="close" size="16" color="#e53935"></t-icon>
          <text>清除</text>
        </view>
      </view>
      <view class="province-options">
        <view class="province-option {{filters.province === 'all' ? 'active' : ''}}" bindtap="setProvince" data-value="all">全国</view>
        <view class="province-option {{filters.province === '四川' ? 'active' : ''}}" bindtap="setProvince" data-value="四川">四川</view>
        <view class="province-option {{filters.province === '北京' ? 'active' : ''}}" bindtap="setProvince" data-value="北京">北京</view>
        <view class="province-option {{filters.province === '上海' ? 'active' : ''}}" bindtap="setProvince" data-value="上海">上海</view>
        <view class="province-option {{filters.province === '广东' ? 'active' : ''}}" bindtap="setProvince" data-value="广东">广东</view>
        <view class="province-option {{filters.province === '江苏' ? 'active' : ''}}" bindtap="setProvince" data-value="江苏">江苏</view>
        <view class="province-option {{filters.province === '浙江' ? 'active' : ''}}" bindtap="setProvince" data-value="浙江">浙江</view>

        <view class="province-option {{filters.province === '河北' ? 'active' : ''}}" bindtap="setProvince" data-value="河北">河北</view>
        <view class="province-option {{filters.province === '山东' ? 'active' : ''}}" bindtap="setProvince" data-value="山东">山东</view>
        <view class="province-option {{filters.province === '河南' ? 'active' : ''}}" bindtap="setProvince" data-value="河南">河南</view>
        <view class="province-option {{filters.province === '湖北' ? 'active' : ''}}" bindtap="setProvince" data-value="湖北">湖北</view>
        <view class="province-option {{filters.province === '湖南' ? 'active' : ''}}" bindtap="setProvince" data-value="湖南">湖南</view>
        <view class="province-option {{filters.province === '安徽' ? 'active' : ''}}" bindtap="setProvince" data-value="安徽">安徽</view>
        <view class="province-option {{filters.province === '福建' ? 'active' : ''}}" bindtap="setProvince" data-value="福建">福建</view>
        <view class="province-option {{filters.province === '江西' ? 'active' : ''}}" bindtap="setProvince" data-value="江西">江西</view>
        <view class="province-option {{filters.province === '广西' ? 'active' : ''}}" bindtap="setProvince" data-value="广西">广西</view>
        <view class="province-option {{filters.province === '海南' ? 'active' : ''}}" bindtap="setProvince" data-value="海南">海南</view>
        <view class="province-option {{filters.province === '贵州' ? 'active' : ''}}" bindtap="setProvince" data-value="贵州">贵州</view>
        <view class="province-option {{filters.province === '云南' ? 'active' : ''}}" bindtap="setProvince" data-value="云南">云南</view>
        <view class="province-option {{filters.province === '重庆' ? 'active' : ''}}" bindtap="setProvince" data-value="重庆">重庆</view>
        <view class="province-option {{filters.province === '天津' ? 'active' : ''}}" bindtap="setProvince" data-value="天津">天津</view>
        <view class="province-option {{filters.province === '辽宁' ? 'active' : ''}}" bindtap="setProvince" data-value="辽宁">辽宁</view>
        <view class="province-option {{filters.province === '吉林' ? 'active' : ''}}" bindtap="setProvince" data-value="吉林">吉林</view>
        <view class="province-option {{filters.province === '黑龙江' ? 'active' : ''}}" bindtap="setProvince" data-value="黑龙江">黑龙江</view>
        <view class="province-option {{filters.province === '陕西' ? 'active' : ''}}" bindtap="setProvince" data-value="陕西">陕西</view>
        <view class="province-option {{filters.province === '甘肃' ? 'active' : ''}}" bindtap="setProvince" data-value="甘肃">甘肃</view>
        <view class="province-option {{filters.province === '青海' ? 'active' : ''}}" bindtap="setProvince" data-value="青海">青海</view>
        <view class="province-option {{filters.province === '山西' ? 'active' : ''}}" bindtap="setProvince" data-value="山西">山西</view>
        <view class="province-option {{filters.province === '内蒙古' ? 'active' : ''}}" bindtap="setProvince" data-value="内蒙古">内蒙古</view>
        <view class="province-option {{filters.province === '宁夏' ? 'active' : ''}}" bindtap="setProvince" data-value="宁夏">宁夏</view>
        <view class="province-option {{filters.province === '新疆' ? 'active' : ''}}" bindtap="setProvince" data-value="新疆">新疆</view>
        <view class="province-option {{filters.province === '西藏' ? 'active' : ''}}" bindtap="setProvince" data-value="西藏">西藏</view>



      </view>
    </view>

    <!-- 其他筛选条件可以在这里添加 -->
  </view>

  <!-- 筛选操作按钮 -->
  <view class="filter-actions">
    <view class="reset-btn" bindtap="resetFilters">重置</view>
    <view class="confirm-btn" bindtap="applyFilters">确定</view>
  </view>
</view>

<!-- 筛选遮罩层 -->
<view class="filter-mask {{showFilter ? 'show' : ''}}" bindtap="closeFilter" catch:touchmove="{{showFilter ? 'preventTouchMove' : ''}}"></view>

<view class="container {{showFilter ? 'showFilter' : ''}}" style="padding-top:{{topPadding}}px;">
  <!-- 搜索栏和标签栏容器 -->
  <view class="top-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-box">
        <icon type="search" size="16" color="#006400" bindtap="onSearch"></icon>
        <input class="search-input" placeholder="搜索植物名称" placeholder-class="search-placeholder" value="{{searchValue}}" bindinput="onSearchChange" bindconfirm="onSearch" bindfocus="onSearchFocus" bindblur="onSearchBlur" />
        <view wx:if="{{searchValue}}" class="search-clear" bindtap="clearSearch">
          <icon type="clear" size="14" color="#999"></icon>
        </view>
        <!-- 搜索按钮 -->
        <view class="search-btn-inner" bindtap="onSearch">搜索</view>
      </view>

      <!-- 筛选按钮 -->
      <view class="filter-btn {{showFilter ? 'active' : ''}}" bindtap="toggleFilter">
        <t-icon name="filter" size="20" color="{{showFilter ? '#fff' : '#006400'}}"></t-icon>
        <text>筛选</text>
      </view>
    </view>

    <!-- 植物名称推荐列表 -->
    <view class="plant-suggestions-container" wx:if="{{showSuggestions && plantSuggestions.length > 0}}" catchtap="stopPropagation" catchtouchmove="stopPropagation" catchlongpress="stopPropagation">
      <view class="suggestions-header">
        <text class="suggestions-title">你想搜索 (共{{plantSuggestions.length}}项)</text>
        <view class="suggestions-close" bindtap="closeSuggestions">×</view>
      </view>
      <scroll-view scroll-y class="suggestions-scroll" catchtouchmove="stopPropagation">
        <view class="suggestion-item" wx:for="{{plantSuggestions}}" wx:key="index" hover-class="suggestion-item-hover" catchtap="onSelectSuggestion" data-name="{{item.name}}">
          <text class="suggestion-name">{{item.name}}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 分类标签 -->
    <!-- <scroll-view scroll-x class="tabs-scroll" show-scrollbar="{{false}}" enhanced>
      <view class="tabs">
        <view 
          class="tab-item {{tabActive === 0 ? 'active' : ''}}" 
          bindtap="onTabChange" 
          data-index="0"
        >
          <text>全部</text>
          <view class="tab-line" wx:if="{{tabActive === 0}}"></view>
        </view>
      </view>
    </scroll-view> -->
  </view>

  <!-- 需求列表 -->
  <view class="demand-list">
    <block wx:if="{{demandList.length > 0}}">
      <view wx:for="{{demandList}}" wx:key="id" class="demand-item {{index % 3 === 0 ? '' : (index % 3 === 1 ? 'color-blue' : 'color-orange')}}" bindtap="onDemandItemTap" data-id="{{item.id}}">

        <!-- 需求项整体采用flex布局 -->
        <view class="demand-item-container">
          <!-- 左侧内容区域 -->
          <view class="demand-item-left">
            <!-- 标题和联系按钮的容器 -->
            <view class="title-contact-container">
              <!-- 标题卡片与数量一体化 -->
              <view class="title-section">
                <view class="title-card">
                  <view class="title-text">
                    <text class="demand-prefix">【求购】</text>
                    <text class="demand-content">{{item.title}}</text>
                  </view>
                </view>
              </view>

              <!-- 联系按钮移动到这里（最右边） -->
              <view class="contact-btn" catchtap="onContactTap" data-phone="{{item.phoneNumber}}" data-name="{{item.contactName}}" wx:if="{{item.phoneNumber}}">
                <t-icon name="call" size="32rpx" color="#4CAF50" />
                <text>联系</text>
              </view>
            </view>

          </view>


        </view>

        <!-- 分割线 -->
        <view class="item-divider"></view>

        <!-- 主要内容区域 -->
        <view class="demand-item-content">
          <!-- 图片和内容布局 -->
          <view class="image-content-container">
            <!-- 预览图 - 有图片时显示 -->
            <image wx:if="{{item.imageList && item.imageList.length > 0}}" src="{{item.imageList[0]}}" mode="aspectFill" class="preview-image" catchtap="onDemandItemTap" data-id="{{item.id}}" style="position: relative; left: 0rpx; top: 0rpx"></image>

            <!-- 没有图片时显示默认图片 -->
            <image wx:else src="/images/center_demand.png" mode="aspectFit" class="default-image" catchtap="onDemandItemTap" data-id="{{item.id}}" style="position: relative; left: 0rpx; top: 0rpx"></image>

            <view class="content-specs-container">
              <!-- 规格信息 -->

              <!-- 规格信息区域 - 统一使用with-image样式 -->
              <view class="specs-info-with-image">

                <view class="spec-item" wx:if="{{item.meter_diameter}}">
                  <text class="spec-label">米径:</text>
                  <text class="spec-value spec-value-highlight">{{item.meter_diameter}}公分</text>
                </view>
                <view class="spec-item" wx:if="{{item.ground_diameter}}">
                  <text class="spec-label">地径:</text>
                  <text class="spec-value spec-value-highlight">{{item.ground_diameter}}公分</text>
                </view>
                <view class="spec-item" wx:if="{{item.height}}">
                  <text class="spec-label">高度:</text>
                  <text class="spec-value">{{item.height}}公分</text>
                </view>
                <view class="spec-item" wx:if="{{item.canopy}}">
                  <text class="spec-label">冠幅:</text>
                  <text class="spec-value">{{item.canopy}}公分</text>
                </view>



                <view class="spec-item" wx:if="{{item.cup}}">
                  <text class="spec-label">杯口:</text>
                  <text class="spec-value">{{item.cup}}杯</text>
                </view>
                <view class="spec-item" wx:if="{{item.branchPos}}">
                  <text class="spec-label">分枝:</text>
                  <text class="spec-value">{{item.branchPos}}公分</text>
                </view>
                <view class="spec-item" wx:if="{{item.quantity}}">
                  <text class="spec-label">数量:</text>
                  <text class="spec-value">{{item.quantity}}{{item.unit}}</text>
                </view>
                <view class="spec-item" wx:if="{{item.quality}}">
                  <text class="spec-label">品质:</text>
                  <text class="quality-value">{{item.quality}}</text>
                </view>
              </view>

              <!-- 简介内容移到规格下方 -->
              <view class="content-container" wx:if="{{item.content}}">
                <text class="content-label">简介:</text>
                <view class="content-wrapper">
                  <text class="content">{{item.content}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 分割线 -->
        <view class="item-divider"></view>

        <!-- 底部信息区域 -->
        <view class="demand-item-footer">
          <!-- 主要信息行 -->
          <view class="footer-main-row">
            <!-- 左侧：时间和地点 -->
            <view class="footer-info-group">
              <view class="info-item time-item">
                <t-icon name="time" size="28rpx" color="#666"></t-icon>
                <text class="info-text">{{item.timeAgo}}</text>
              </view>
              <view class="info-item location-item" wx:if="{{item.buyArea}}">
                <text class="location-label">求购地</text>
                <text class="info-text location-text">{{item.buyArea}}</text>
              </view>
            </view>

            <!-- 右侧：状态标签 -->
            <view class="footer-status-group">
              <!-- 回价数量徽章 -->
              <view class="status-badge reply-badge">
                <text class="badge-label" style="color: green;">报价数</text>
                <text class="badge-text" style="color:green">{{item.rawData.replyCount || 0}}</text>
              </view>

              <!-- 期限标签 -->
              <view class="status-badge deadline-badge" wx:if="{{item.remainingTime}}">
                <text class="badge-text" style="color: orange;">{{item.remainingTime}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部区域：理想价格和回价按钮 -->
        <view class="demand-item-buttons">
          <!-- 理想价格移动到这里（左侧） -->
          <view class="price-tag" wx:if="{{item.rawData.price !== null && item.rawData.price !== undefined && item.rawData.price !== 0 && item.rawData.price !== ''}}">
            <text class="price-label">理想价：</text>{{item.rawData.price}}元
          </view>
          <!-- 当没有理想价格时，添加占位元素保持布局平衡 -->
          <view class="price-placeholder" wx:if="{{item.rawData.price === null || item.rawData.price === undefined || item.rawData.price === 0 || item.rawData.price === ''}}"></view>
          <!-- 回价按钮保持在右侧 -->
          <view class="reply-price-btn" catchtap="onReplyPriceTap" data-id="{{item.id}}">
          <!-- #elif 颜色备份 ： ff9800 4CAF50 -->
            <t-icon name="add" size="32rpx" color="#4CAF50" />
            <text>报价</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:if="{{!loading && demandList.length === 0}}" class="empty-state">
      <t-icon name="info-circle" size="80rpx" color="#006400"></t-icon>
      <view class="empty-text">暂无需求信息</view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 加载更多/无更多数据 -->
    <view wx:if="{{!loading && hasMore && demandList.length > 0 && !autoLoading}}" class="load-more" bindtap="loadMoreData">
      点击加载更多
    </view>
    <view wx:elif="{{!loading && !hasMore && demandList.length > 0}}" class="no-more">
      没有更多数据了
    </view>
  </view>

  <!-- 发布按钮 -->
  <!-- <view class="publish-btn" hover-class="publish-btn-hover" bindtap="onPublishTap" wx:if="{{!showFilter}}">
    <t-icon name="add" size="28" color="#fff"></t-icon>
    <text>发布需求</text>
  </view> -->

  <!-- 回到顶部 -->
  <view class="back-top" hover-class="back-top-hover" wx:if="{{scrollTop > 300 && !showFilter}}" bindtap="onBackTop">
    <t-icon name="arrow-up" size="35" color="#ff0000"></t-icon>
  </view>
  <!-- TDesign风格分割线 -->
  <view class="t-divider">
    <view class="t-divider-before"></view>
    <view class="t-divider-text">{{dividerText}}</view>
    <view class="t-divider-after"></view>
  </view>
</view>

<!-- 自定义tabbar -->
<custom-tab-bar></custom-tab-bar>