/* pages/tutorial/tutorial.wxss */

/* 页面容器 */
.tutorial-container {
  padding: 0;
  background-color: #f4f9f4; /* 与demand页面一致的浅绿色背景 */
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏样式 - 与demand页面一致 */
.custom-nav {
  background: #43a047 !important; /* 纯色背景 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
  z-index: 1000 !important; /* 确保最高层级 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
}

.custom-nav::after {
  display: none;
}

/* 导航栏内部样式 */
.weui-navigation-bar__inner {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.weui-navigation-bar__center {
  color: #ffffff !important; /* 白色文字 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
  font-weight: bold;
}

/* 防遮挡占位元素 */
.nav-placeholder {
  width: 100%;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 40rpx 30rpx;
}

/* 空内容提示 */
.empty-content {
  text-align: center;
  padding: 120rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #666666;
  margin: 30rpx 0 20rpx;
  font-weight: 500;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #999999;
  line-height: 1.5;
}







/* 教程分类 */
.tutorial-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
  padding: 30rpx 0 20rpx;
  border-bottom: 1rpx solid #e0e0e0;
  margin-bottom: 20rpx;
}

.section-title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-left: 12rpx;
}

/* 教程列表 */
.tutorial-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 教程项目样式 */
.tutorial-item {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

/* 教程头部（可点击区域） */
.tutorial-header {
  transition: all 0.3s ease;
}

.tutorial-header-hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.12);
}

/* 视频缩略图 */
.tutorial-thumbnail {
  position: relative;
  width: 100%;
  height: 350rpx;
  overflow: hidden;
  transition: height 0.3s ease;
}




/* 教程视频播放器 */
.tutorial-video {
  width: 100%;
  height: 100%;
  border-radius: 0;
}



/* 教程内容样式 */
.tutorial-content {
  padding: 24rpx;
}

.tutorial-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.tutorial-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.tutorial-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-text {
  font-size: 22rpx;
  color: #999999;
  margin-left: 6rpx;
}


