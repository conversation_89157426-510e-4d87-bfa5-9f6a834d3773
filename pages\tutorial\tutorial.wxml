<!-- pages/tutorial/tutorial.wxml -->
<page-meta>
  <navigation-bar title="视频教程" back="{{true}}"  bind:back="goBack" homeButton="{{false}}" extClass="custom-nav" hideTree="{{true}}"></navigation-bar>
</page-meta>

<!-- 防遮挡占位元素 -->
<view class="nav-placeholder" style="height:{{topPadding}}px;"></view>

<!-- 主要内容区域 -->
<view class="tutorial-container">


  <!-- 教程列表 -->
  <view class="content-area">
    <!-- 发布教程分类 -->
    <view class="tutorial-section">
      <view class="section-title">
        <t-icon name="edit" size="20" color="#4CAF50"></t-icon>
        <text class="section-title-text">发布教程</text>
      </view>
      <view class="tutorial-list">
        <view
          class="tutorial-item"
          wx:for="{{tutorialList}}"
          wx:key="id"
          wx:if="{{item.category === 'publish'}}"
        >
          <!-- 教程缩略图和基本信息 -->
          <view class="tutorial-header">
            <view class="tutorial-thumbnail">
              <!-- 视频播放器 -->
              <video
                id="video-player-{{item.id}}"
                class="tutorial-video"
                src="{{item.videoUrl}}"
                poster="{{item.thumbnail || (item.videoUrl + '?vframe/jpg/offset/0')}}"
                controls="{{true}}"
                autoplay="{{false}}"
                loop="{{false}}"
                muted="{{false}}"
                initial-time="0.01"
                show-center-play-btn="{{true}}"
                show-play-btn="{{true}}"
                show-fullscreen-btn="{{true}}"
                show-progress="{{true}}"
                enable-progress-gesture="{{true}}"
                object-fit="cover"
                bindplay="onVideoPlay"
                bindpause="onVideoPause"
                bindended="onVideoEnded"
                binderror="onVideoError"
                bindtimeupdate="onVideoTimeUpdate"
                data-id="{{item.id}}"
              ></video>
            </view>
            <view class="tutorial-content">
              <view class="tutorial-title">{{item.title}}</view>
              <view class="tutorial-desc">{{item.description}}</view>
              <view class="tutorial-meta">
                <view class="meta-item">
                  <t-icon name="time" size="14" color="#999"></t-icon>
                  <text class="meta-text">{{item.duration}}</text>
                </view>
                <view class="meta-item">
                  <t-icon name="{{item.icon}}" size="14" color="#4CAF50"></t-icon>
                  <text class="meta-text">{{item.category === 'publish' ? '发布' : '使用'}}</text>
                </view>
              </view>
            </view>
          </view>

        </view>
      </view>
    </view>

    <!-- 使用教程分类 -->
    <view class="tutorial-section">
      <view class="section-title">
        <t-icon name="help-circle" size="20" color="#4CAF50"></t-icon>
        <text class="section-title-text">使用教程</text>
      </view>
      <view class="tutorial-list">
        <view
          class="tutorial-item"
          wx:for="{{tutorialList}}"
          wx:key="id"
          wx:if="{{item.category === 'usage'}}"
        >
          <!-- 教程缩略图和基本信息 -->
          <view class="tutorial-header">
            <view class="tutorial-thumbnail">
              <!-- 视频播放器 -->
              <video
                id="video-player-{{item.id}}"
                class="tutorial-video"
                src="{{item.videoUrl}}"
                poster="{{item.thumbnail || (item.videoUrl + '?vframe/jpg/offset/0')}}"
                controls="{{true}}"
                autoplay="{{false}}"
                loop="{{false}}"
                muted="{{false}}"
                initial-time="0.01"
                show-center-play-btn="{{true}}"
                show-play-btn="{{true}}"
                show-fullscreen-btn="{{true}}"
                show-progress="{{true}}"
                enable-progress-gesture="{{true}}"
                object-fit="cover"
                bindplay="onVideoPlay"
                bindpause="onVideoPause"
                bindended="onVideoEnded"
                binderror="onVideoError"
                bindtimeupdate="onVideoTimeUpdate"
                data-id="{{item.id}}"
              ></video>
            </view>
            <view class="tutorial-content">
              <view class="tutorial-title">{{item.title}}</view>
              <view class="tutorial-desc">{{item.description}}</view>
              <view class="tutorial-meta">
                <view class="meta-item">
                  <t-icon name="time" size="14" color="#999"></t-icon>
                  <text class="meta-text">{{item.duration}}</text>
                </view>
                <view class="meta-item">
                  <t-icon name="{{item.icon}}" size="14" color="#4CAF50"></t-icon>
                  <text class="meta-text">{{item.category === 'publish' ? '发布' : '使用'}}</text>
                </view>
              </view>
            </view>
          </view>

        </view>
      </view>
    </view>
  </view>
</view>
