Page({
  /**
   * 页面的初始数据
   */
  data: {
    demandList: [], // 求购列表
    loading: false, // 加载状态
    page: 1, // 当前页码
    pageSize: 10, // 每页数量
    hasMore: true, // 是否有更多数据
    safeBottomHeight: 60, // 底部安全区域高度（单位rpx）
    searchKeyword: '', // 搜索关键词
    originalDemandList: [], // 保存原始求购列表，用于搜索筛选
    total: 0, // 总记录数（用于显示求购数量）
    totalViews: 0, // 总浏览量
    isLogined: false, // 是否已登录
    userInfo: null, // 用户信息
    navHeight: 0, // 导航栏高度
    statusBarHeight: 0, // 状态栏高度
    weatherData: {}, // 天气数据
    weatherAnimation: true, // 天气图标动画状态
    weatherKey: 'ecea50b80c1543fdb0dac64b3d649332', // 和风天气API的key
    weatherLastUpdateTime: 0, // 天气数据最后更新时间戳
    registerTimeText: '', // 用户注册时间文本
    // 移除totalNewReplies，统一使用云函数聚合查询管理红点状态
  },

  // 添加变量跟踪天气信息请求时间
  lastWeatherInfoRequestTime: Date.now() - 10000, // 初始化为当前时间减去10秒，避免首次加载时触发频繁请求提示

  // 添加变量跟踪通过下拉刷新获取天气的时间
  lastPullRefreshWeatherTime: 0,

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 初始化地图工具
    this.mapUtils = require('../../../utils/mapUtils.js');
    
    // 设置底部安全区域高度
    this.setSafeBottomHeight();
    
    // 获取设备信息，用于自适应布局
    this.setSystemInfo();
    
    // 确保底部tabBar正确显示"我"的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        active: 3,  // "我"页面是第四个选项，索引为3
        hidden: false // 确保tabbar不隐藏
      });
    }
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 不管是否登录，都尝试加载数据
    this.setData({
      page: 1,
      demandList: [],
      hasMore: true
    });
    this.loadDemandList();
    
    // 获取天气信息
    this.getWeatherInfo();
    
    // 启动天气图标动画
    this.startWeatherAnimation();
    
    // 获取用户注册时间
    if (this.data.isLogined && this.data.userInfo) {
      this.fetchUserRegisterTime();
    }
  },

  /**
   * 获取系统信息，用于自适应布局
   */
  setSystemInfo: function() {
    // 替换已弃用的wx.getSystemInfoSync()
    const deviceInfo = wx.getDeviceInfo();
    const windowInfo = wx.getWindowInfo();
    
    // 获取状态栏高度
    const statusBarHeight = windowInfo.statusBarHeight;
    // 默认导航栏高度
    const navHeight = (deviceInfo.platform === 'android' ? 48 : 44) + statusBarHeight;
    
    this.setData({
      statusBarHeight,
      navHeight
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 检查登录状态
    this.checkLoginStatus();
    
    // 不管是否登录，都尝试加载数据
    this.setData({
      page: 1,
      demandList: [],
      hasMore: true
    });
    this.loadDemandList();
    
    // 确保底部tabBar正确显示"我"的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        active: 3,  // "我"页面是第四个选项，索引为3
        hidden: false // 确保tabbar不隐藏
      });
    }
    
    // 仅在以下情况获取天气数据：
    // 1. 已登录状态
    // 2. 当前页面没有天气数据
    // 3. 距离上次请求已经超过一定时间
    const currentTime = new Date().getTime();
    const timeSinceLastRequest = currentTime - this.lastWeatherInfoRequestTime;
    const WEATHER_REFRESH_INTERVAL = 10 * 60 * 1000; // 10分钟刷新一次

    if (this.data.isLogined && 
        (!this.data.weatherData.temp || timeSinceLastRequest > WEATHER_REFRESH_INTERVAL)) {
      this.getWeatherInfo();
    }
    
    // 获取全局登录状态，确保头像实时更新
    const app = getApp();
    
    // 只有在全局已登录状态下才更新用户信息
    if (app.globalData.isLogined && app.globalData.userInfo && app.globalData.userId) {
      // 使用缓存的头像URL避免闪烁
      let userInfoWithAvatar = {...app.globalData.userInfo};
      if (app.globalData.cachedAvatarUrl) {
        userInfoWithAvatar.avatarUrl = app.globalData.cachedAvatarUrl;
      }

      // 立即使用全局数据更新UI，确保头像实时显示
      this.setData({
        isLogined: true,
        userInfo: userInfoWithAvatar
      });
      
      // 获取用户注册时间
      this.fetchUserRegisterTime();
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    // 获取App实例
    const app = getApp();
    
    // 从全局状态中获取登录状态
    if (app.globalData.isLogined && app.globalData.userInfo && app.globalData.userId) {
      // 使用缓存的头像URL避免闪烁
      let userInfoWithAvatar = {...app.globalData.userInfo};
      if (app.globalData.cachedAvatarUrl) {
        userInfoWithAvatar.avatarUrl = app.globalData.cachedAvatarUrl;
      }

      this.setData({
        isLogined: true,
        userInfo: userInfoWithAvatar
      });
      
      // 获取用户注册时间
      this.fetchUserRegisterTime();
      
      return true;
    } else {
      this.setData({
        isLogined: false,
        userInfo: null,
        registerTimeText: ''
      });
      return false;
    }
  },

  /**
   * 获取用户注册时间并计算至今时长
   */
  fetchUserRegisterTime: function() {
    const app = getApp();
    if (!app.globalData.userId) return;
    
    const db = wx.cloud.database();
    db.collection('users')
      .doc(app.globalData.userId)
      .field({
        createTime: true
      })
      .get()
      .then(res => {
        if (res.data && res.data.createTime) {
          // 计算注册时间
          const registerTime = this.calculateRegisterTime(res.data.createTime);
          this.setData({
            registerTimeText: registerTime
          });
        }
      })
      .catch(err => {
        console.error('获取用户注册时间失败:', err);
      });
  },

  /**
   * 计算注册时间
   * @param {Date|Object} createTime - 创建时间，可能是Date对象或云数据库时间对象
   * @returns {string} - 格式化的注册时间文本
   */
  calculateRegisterTime: function(createTime) {
    let createDate;
    
    // 处理云数据库时间对象
    if (createTime && createTime.$date) {
      createDate = new Date(createTime.$date);
    } 
    // 处理普通Date对象或时间戳
    else if (createTime instanceof Date) {
      createDate = createTime;
    } else if (typeof createTime === 'number') {
      createDate = new Date(createTime);
    } else {
      // 如果都不是，尝试直接创建Date对象
      try {
        createDate = new Date(createTime);
      } catch(e) {
        console.error('无法解析创建时间:', e);
        return '新用户';
      }
    }
    
    // 确保解析成功
    if (isNaN(createDate.getTime())) {
      return '新用户';
    }
    
    const now = new Date();
    const diffTime = Math.abs(now - createDate);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 1) {
      return '今日注册';
    } else if (diffDays < 30) {
      return `${diffDays}天前注册`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months}个月前注册`;
    } else {
      const years = Math.floor(diffDays / 365);
      return `${years}年前注册`;
    }
  },

  /**
   * 获取用户最新信息，包括求购数量
   * 注意：我们不再需要这个方法，因为求购数量直接从loadDemandList返回的total获取
   * 但保留方法以防其他地方调用，只是移除了对supplyCount的处理
   */
  fetchUserLatestInfo: function() {
    const app = getApp();
    if (!app.globalData.userId) return;
    
    const db = wx.cloud.database();
    db.collection('users')
      .doc(app.globalData.userId)
      .field({
        avatarUrl: true,
        nickName: true
      })
      .get()
      .then(res => {
        if (res.data) {
          // 使用缓存的头像URL，但更新其他信息
          let updatedUserInfo = { ...this.data.userInfo, ...res.data };
          
          if (app.globalData.cachedAvatarUrl) {
            updatedUserInfo.avatarUrl = app.globalData.cachedAvatarUrl;
          }
          
          this.setData({
            userInfo: updatedUserInfo
          });
        }
      })
      .catch(err => {
        console.error('获取用户最新信息失败:', err);
      });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.setData({ 
      page: 1,
      demandList: [],
      hasMore: true
    });
    this.loadDemandList().then(() => {
      wx.stopPullDownRefresh();
    });
    
    // 检查是否可以通过下拉刷新更新天气数据
    if (this.data.isLogined) {
      const currentTime = new Date().getTime();
      // 设置30分钟(1800000ms)的刷新限制
      const PULL_REFRESH_LIMIT = 30 * 60 * 1000; 
      
      // 检查是否超过了限制时间
      if (currentTime - this.lastPullRefreshWeatherTime >= PULL_REFRESH_LIMIT) {
        // 更新天气数据
        this.getWeatherInfo(true);
        // 更新最后一次通过下拉刷新获取天气的时间
        this.lastPullRefreshWeatherTime = currentTime;
      
      }
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  /**
   * 从云函数加载求购列表数据
   * 排序逻辑：优先显示有新回价的求购(hasNewReply=true)，然后按创建时间降序排列
   */
  loadDemandList: function() {
    // 如果未登录，显示加载失败提示，但不跳转页面
    if (!this.data.isLogined) {
      this.setData({ 
        loading: false,
        demandList: [],
        hasMore: false,
        total: 0, // 重置total为0
      });
      
      // 显示加载失败提示
      wx.showToast({
        title: '请先登录后查看',
        icon: 'none',
        duration: 2000
      });
      
      return Promise.resolve();
    }
    
    const { page, pageSize, searchKeyword } = this.data;
    
    this.setData({ loading: true });
    
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: {
          type: 'getMyDemandList',
          page,
          pageSize,
          searchKeyword
        },
        success: res => {
          const result = res.result || {};
          if (result.code === 0) {
            const newData = result.data || [];
            
            // 处理数据，格式化日期和计算新回价数量
            const processedData = this.processNewReplyCount(newData);

            // 注意：不再在这里计算总的新回价数量
            // 总的红点统计由用户主页面的聚合查询统一处理
            // 这里只处理当前页面的显示数据

            this.setData({
              demandList: page === 1 ? processedData : [...this.data.demandList, ...processedData],
              originalDemandList: page === 1 ? processedData : [...this.data.originalDemandList, ...processedData],
              hasMore: result.hasMore,
              total: result.total, // 更新total用于显示求购数量
              // 移除totalNewReplies设置，红点统计由用户主页面的聚合查询统一处理
              loading: false
            });



            // 通知user页面更新红点状态
            this.notifyUserPageUpdate();
            
            // 如果是第一页且没有数据，显示空状态提示
            if (page === 1 && newData.length === 0) {
              wx.showToast({
                title: '暂无求购信息',
                icon: 'none'
              });
            }
          } else {
            // 显示错误提示
            wx.showToast({
              title: result.msg || '加载失败',
              icon: 'none'
            });
            this.setData({ loading: false });
          }
          resolve();
        },
        fail: err => {
          wx.showToast({
            title: '网络异常，请重试',
            icon: 'none'
          });
          this.setData({ loading: false });
          reject(err);
        }
      });
    });
  },

  /**
   * 处理新回价数量计算
   * @param {Array} demandList - 需求列表
   * @returns {Array} - 处理后的需求列表
   */
  processNewReplyCount: function(demandList) {
    return demandList.map(item => {
      // 格式化创建时间
      if (item.createTime) {
        item.formattedTime = this.formatDate(new Date(item.createTime));
      }

      // 直接使用数据库字段
      const newReplyCount = Math.max(0, item.newReplyCount || 0);
      const hasNewReply = newReplyCount > 0;

      return {
        ...item,
        newReplyCount: newReplyCount,
        hasNewReply: hasNewReply
      };
    });
  },

  /**
   * 格式化日期
   * @param {Date} date - 日期对象
   * @returns {string} - 格式化的日期字符串 (YYYY-MM-DD)
   */
  formatDate: function(date) {
    if (!date || isNaN(date.getTime())) {
      return '无日期';
    }
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
  },

  /**
   * 通知user页面更新红点状态（使用优化后的聚合查询）
   */
  notifyUserPageUpdate: function() {
    const pages = getCurrentPages();
    const userPage = pages.find(page => page.route === 'pages/user/user');
    if (userPage && typeof userPage.updateDemandReplyStatus === 'function') {
      // 调用优化后的红点更新函数
      userPage.updateDemandReplyStatus();
    }
  },

  /**
   * 刷新新回价状态（从详情页返回时调用）
   * 优化版：重新加载当前页面数据并通知user页面使用聚合查询更新
   */
  refreshNewReplyStatus: function() {
    // 重新处理当前列表的显示数据（保留单个帖子的红点显示）
    const processedData = this.processNewReplyCount(this.data.demandList);

    this.setData({
      demandList: processedData
      // 移除totalNewReplies本地计算，统一使用云函数聚合查询
    });

    // 通知user页面使用聚合查询更新全局红点状态
    // 这样可以确保统计所有页面的红点，解决分页问题
    this.notifyUserPageUpdate();
  },



  /**
   * 加载更多数据
   */
  loadMore: function() {
    this.setData({ page: this.data.page + 1 }, () => {
      this.loadDemandList();
    });
  },

  /**
   * 查看详情
   */
  viewDetail: function(e) {
    // 检查登录状态
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录后查看',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    const id = e.currentTarget.dataset.id;
   
    wx.navigateTo({
      url: `/pages/demand/detail/detail?id=${id}`
    });
  },

  /**
   * 更新求购
   */
  updateDemand: function(e) {
    // 检查登录状态
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录后更新',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    const id = e.currentTarget.dataset.id;
    // 导航到编辑页面
    wx.navigateTo({
      url: `/pages/user/my-demand/edit/edit?id=${id}`
    });
    
    // 添加日志，帮助调试
    console.log('正在跳转到编辑页面，ID:', id);
  },

  /**
   * 下架求购
   */
  removeDemand: function(e) {
    // 检查登录状态
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录后操作',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    const id = e.currentTarget.dataset.id;
    
    // 获取当前求购项
    const demandItem = this.data.demandList.find(item => item._id === id);
    
    if (!demandItem) {
      wx.showToast({
        title: '找不到该求购信息',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条求购信息吗？删除后无法恢复。',
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          });
          
          // 获取数据库引用
          const db = wx.cloud.database();
          
          db.collection('demand_content').doc(id).remove()
            .then(() => {
              console.log('删除数据库记录成功');
              // 删除对应的Notice记录
              return this.deleteRelatedNotice(id);
            })
            .then(() => {
              console.log('删除Notice记录成功');
              return this.deleteDemandReplyFolder(id);
            })
            .then(() => {
              console.log('删除回价文件夹成功');
              return this.deleteDemandReplies(id);
            })
            .then(() => {
              console.log('删除回价数据库记录成功');
              return this.deleteDemandImages(demandItem);
            })
            .then(() => {
              console.log('删除求购图片成功');
              return this.updateUserDemandCount();
            })
            .then(() => {


              // 4. 从前端列表中移除该项
              const updatedList = this.data.demandList.filter(item => item._id !== id);

              this.setData({
                demandList: updatedList,
                total: updatedList.length
              });

              wx.hideLoading();
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            })
            .catch(err => {
              console.error('删除失败:', err);
              wx.hideLoading();
              wx.showToast({
                title: '删除失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  /**
   * 删除相关的Notice记录
   * @param {String} postId - 求购帖子ID
   */
  deleteRelatedNotice: function(postId) {
    const db = wx.cloud.database();

    // 删除notice集合中对应的记录，使用精确的筛选条件
    return db.collection('notice').where({
      postId: postId,
      postType: 'demand'  // 确保只删除demand类型的notice
    }).remove().then(res => {
      console.log('删除Demand Notice记录成功:', res);
      if (res.stats && res.stats.removed > 0) {
        console.log(`成功删除 ${res.stats.removed} 条Demand Notice记录`);
      } else {
        console.log('没有找到对应的Demand Notice记录');
      }
      return Promise.resolve();
    }).catch(err => {
      console.error('删除Demand Notice记录失败:', err);
      // Notice删除失败不影响主流程，继续执行
      return Promise.resolve();
    });
  },

  deleteDemandReplies: function(demandId) {
    const db = wx.cloud.database();

    return db.collection('demand_reply')
      .where({
        demand_PostId: demandId
      })
      .get()
      .then(res => {
        if (res.data.length === 0) {
          return Promise.resolve();
        }

        const deletePromises = res.data.map(reply => {
          return db.collection('demand_reply').doc(reply._id).remove();
        });

        return Promise.all(deletePromises);
      })
      .then(() => {
        return Promise.resolve();
      })
      .catch(err => {
        console.error('删除回价数据库记录失败:', err);
        return Promise.resolve();
      });
  },

  deleteDemandReplyFolder: function(demandId) {
    const folderPath = `demand_replyImages/demandPost_${demandId}`;

    return wx.cloud.callFunction({
      name: 'quickstartFunctions',
      config: {
        env: wx.cloud.DYNAMIC_CURRENT_ENV
      },
      data: {
        type: 'deleteDemandReplyFolder',
        folderPath: folderPath
      }
    }).then(res => {
      return Promise.resolve();
    }).catch(err => {
      console.error('调用云函数删除文件夹失败:', err);
      return Promise.resolve();
    });
  },

  /**
   * 删除求购相关的图片
   * @param {Object} demandItem - 求购项信息
   */
  deleteDemandImages: function(demandItem) {
    // 检查是否有图片需要删除
    if (!demandItem.imageList || demandItem.imageList.length === 0) {
      console.log('没有图片需要删除');
      return Promise.resolve();
    }
    
    // 获取用户ID和求购ID
    const userId = demandItem.uid;
    const demandId = demandItem._id;
    
    // 从图片URL中提取fileID
    const fileIDs = demandItem.imageList.map(imageUrl => {
      // 确保imageUrl是字符串
      if (typeof imageUrl === 'string') {
        return imageUrl;
      }
      return '';
    }).filter(id => id !== '');
    
    // 如果没有有效的fileID，直接返回成功
    if (fileIDs.length === 0) {
      return Promise.resolve();
    }
    
    console.log('准备删除图片:', fileIDs);
    
    // 使用云函数删除文件
    return wx.cloud.deleteFile({
      fileList: fileIDs
    }).then(res => {
      console.log('删除文件结果:', res);
      return res;
    }).catch(err => {
      console.error('删除文件失败:', err);
      // 即使删除文件失败，也继续流程
      return Promise.resolve();
    });
  },



  /**
   * 更新用户的求购数量统计
   */
  updateUserDemandCount: function() {
    // 获取App实例
    const app = getApp();
    const userId = app.globalData.userId;
    
    if (!userId) {
      return Promise.resolve();
    }
    
    const db = wx.cloud.database();
    const _ = db.command;
    
    // 查询用户当前的求购数量
    return db.collection('demand_content')
      .where({
        uid: userId
      })
      .count()
      .then(res => {
        const count = res.total;
        
        // 更新用户表中的求购数量
        return db.collection('users').doc(userId).update({
          data: {
            demandCount: count
          }
        });
      })
      .then(() => {
        console.log('更新用户求购数量成功');
        return Promise.resolve();
      })
      .catch(err => {
        console.error('更新用户求购数量失败:', err);
        return Promise.resolve(); // 继续流程
      });
  },

  /**
   * 导航栏返回按钮事件
   */
  onNavBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },
  
  /**
   * 导航栏首页按钮事件
   */
  onNavHome: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },
  
  /**
   * 跳转到发布求购页面
   */
  goToPublish: function() {
    // 检查登录状态
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录后发布',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/demand/publish/publish'
    });
  },

  /**
   * 跳转到登录页面
   */
  goToLogin: function() {
    wx.switchTab({
      url: '/pages/user/user'
    });
  },

  /**
   * 设置底部安全区域高度
   */
  setSafeBottomHeight: function() {
    try {
      // 使用新API替换已弃用的wx.getSystemInfoSync()
      const windowInfo = wx.getWindowInfo();
      // 检测是否有底部安全区域（如iPhone X系列）
      let safeBottomHeight = 60; // 默认高度
      
      if (windowInfo.safeArea) {
        const safeAreaBottom = windowInfo.safeArea.bottom;
        const screenHeight = windowInfo.screenHeight;
        // 如果安全区域底部与屏幕底部有差距，说明有底部安全区域
        if (screenHeight > safeAreaBottom) {
          safeBottomHeight = (screenHeight - safeAreaBottom) * 2; // 转为rpx
        }
      }
      
      this.setData({ safeBottomHeight });
    } catch (e) {
      console.error('获取系统信息失败', e);
      // 使用默认值
      this.setData({ safeBottomHeight: 60 });
    }
  },

  /**
   * 搜索输入事件处理
   */
  onSearchInput: function(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  /**
   * 清空搜索
   */
  clearSearch: function() {
    this.setData({ 
      searchKeyword: '',
      page: 1,
      demandList: [],
      hasMore: true
    });
    this.loadDemandList();
  },

  /**
   * 执行搜索
   */
  onSearch: function() {
    this.setData({
      page: 1,
      demandList: [],
      hasMore: true
    });
    this.loadDemandList();
  },

  /**
   * 获取天气信息
   * @param {boolean} forceUpdate - 是否强制更新天气数据
   */
  getWeatherInfo: function(forceUpdate = false) {
    // 如果未登录，不获取天气信息
    if (!this.data.isLogined) return;
    
    // 获取当前时间
    const currentTime = new Date().getTime();
    
    // 检查是否在短时间内（3秒）重复调用，除非强制更新
    const MIN_REQUEST_INTERVAL = 3000; // 3秒内不重复请求
    if (!forceUpdate && (currentTime - this.lastWeatherInfoRequestTime < MIN_REQUEST_INTERVAL)) {
      console.log('天气信息请求过于频繁，跳过此次请求');
      return;
    }
    
    // 如果是强制更新，但是通过下拉刷新触发，需要检查是否超过了30分钟限制
    // 注意：这里我们通过比较lastPullRefreshWeatherTime是否为0来判断是否首次加载
    // 首次加载时，lastPullRefreshWeatherTime为0，应该允许获取天气数据
    if (forceUpdate && this.lastPullRefreshWeatherTime !== 0) {
      const PULL_REFRESH_LIMIT = 30 * 60 * 1000; // 30分钟
      if (currentTime - this.lastPullRefreshWeatherTime < PULL_REFRESH_LIMIT) {
        console.log('下拉刷新获取天气数据过于频繁，跳过此次请求');
        return;
      }
    }
    
    // 更新最后请求时间
    this.lastWeatherInfoRequestTime = currentTime;
    
    // 获取当前App实例，用于跨页面共享天气数据
    const app = getApp();
    
    // 检查全局天气数据是否已存在且未过期
    if (!forceUpdate && this.checkWeatherCache()) {
      console.log('使用缓存的天气数据');
      return;
    }
    
    // 使用mapUtils获取当前位置和位置名称
    this.mapUtils.getCurrentLocationAndReverse({
      success: (res) => {
        // 从结果中获取位置信息，按照腾讯地图API的返回结构处理
        const addressComponent = res.result.address_component;
        const location = res.result.location;
        
        const latitude = location.lat;
        const longitude = location.lng;
        const district = addressComponent.district;
        const city = addressComponent.city;
        
        // 更新位置名称
        this.setData({
          'weatherData.location': district || city || '未知位置'
        });
        
        // 获取天气数据
        this.getWeatherData(latitude, longitude);
      },
      fail: (err) => {
        console.error('获取位置失败', err);
        // 设置默认天气数据
        this.setDefaultWeatherData();
      }
    });
  },
  
  /**
   * 检查天气数据缓存是否有效
   * @returns {boolean} - 如果缓存有效返回true，否则返回false
   */
  checkWeatherCache: function() {
    const app = getApp();
    
    // 检查全局是否已有天气数据
    if (app.globalData && app.globalData.weatherData && 
        app.globalData.weatherData.temp && app.globalData.weatherData.text) {
      
      const currentTime = new Date().getTime();
      const lastUpdateTime = app.globalData.weatherLastUpdateTime || 0;
      const timeElapsed = currentTime - lastUpdateTime;
      
      // 如果天气数据是30分钟内获取的，则视为有效
      const CACHE_VALID_PERIOD = 30 * 60 * 1000; // 30分钟，单位毫秒
      
      if (timeElapsed < CACHE_VALID_PERIOD) {
        // 使用全局缓存的天气数据
        this.setData({
          weatherData: {...app.globalData.weatherData}
        });
        return true;
      }
    }
    
    return false;
  },

  // 标记是否正在请求天气数据，防止重复请求
  isRequestingWeather: false,

  /**
   * 获取天气数据
   */
  getWeatherData: function(latitude, longitude) {
    // 防止重复请求
    if (this.isRequestingWeather) {
      console.log('已有天气请求正在进行，跳过此次请求');
      return;
    }
    this.isRequestingWeather = true;
    
    // 使用代理模式调用天气API
    const weatherProxy = {
      // 真实API请求
      realApiRequest: () => {
        const API_HOST = "mh3p3y76gv.re.qweatherapi.com"; // 您的专属API Host
        
        wx.request({
          url: `https://${API_HOST}/v7/weather/now?key=${this.data.weatherKey}&location=${longitude},${latitude}`,
          success: res => {
            // 重置请求标志
            this.isRequestingWeather = false;
            //天气数据
           // console.log('天气API返回数据:', res.data);
            if (res.data && res.data.code === '200' && res.data.now) {
              const weatherData = res.data.now;
              
              // 根据icon代码映射到TDesign图标
              const tDesignIcon = this.mapWeatherIconToTDesign(weatherData.icon);
              
              // 构建完整的天气数据对象
              const completeWeatherData = {
                temp: weatherData.temp,
                text: weatherData.text,
                icon: weatherData.icon,
                tDesignIcon: tDesignIcon,
                location: this.data.weatherData.location,
                tips: this.getWeatherTips(weatherData.text)
              };
              
              // 设置天气数据
              this.setData({
                weatherData: completeWeatherData
              });
              
              // 保存到全局缓存
              const app = getApp();
              app.globalData = app.globalData || {};
              app.globalData.weatherData = completeWeatherData;
              app.globalData.weatherLastUpdateTime = new Date().getTime();
            } else {
              console.error('获取天气数据失败:', res.data);
              // 使用备用方法
              weatherProxy.backupRequest();
            }
          },
          fail: (err) => {
            // 重置请求标志
            this.isRequestingWeather = false;
            
            console.error('天气API请求失败:', err);
            // 使用备用方法
            weatherProxy.backupRequest();
          }
        });
      },
      
      // 备用方法（显示获取失败）
      backupRequest: () => {
        console.log('天气数据获取失败');
        // 重置请求标志
        this.isRequestingWeather = false;
        
        // 显示获取失败
        this.setData({
          'weatherData.temp': '--',
          'weatherData.text': '获取失败',
          'weatherData.icon': '',
          'weatherData.tips': '天气数据暂时无法获取，请稍后再试'
        });
      },
      
      // 代理方法，根据条件决定调用哪个实际方法
      request: () => {
        try {
          // 默认使用真实API
          weatherProxy.realApiRequest();
        } catch (error) {
          console.error('天气API代理错误:', error);
          // 出错时使用备用方法
          weatherProxy.backupRequest();
        }
      }
    };
    
    // 执行代理请求
    weatherProxy.request();
  },

  /**
   * 将和风天气icon代码映射为TDesign图标名称
   * @param {string} iconCode - 和风天气icon代码
   * @returns {string} - 对应的TDesign图标名称
   */
  mapWeatherIconToTDesign: function(iconCode) {
    // 晴天
    if (iconCode === '100') {
      return 'sunny';
    }
    
    // 多云或晴间多云
    if (['101', '102', '103', '104'].includes(iconCode)) {
      return 'cloudy-sunny';
    }
    
    // 各种雨
    if (['300', '301', '305', '306', '307', '308', '309', '310', 
         '311', '312', '313', '314', '315', '316', '317', '318', '399'].includes(iconCode)) {
      return 'rain-medium';
    }
    
    // 雷雨天气
    if (['302', '303', '304'].includes(iconCode)) {
      return 'thunderstorm';
    }
    
    // 其他天气状况
    return 'cloudy-day';
  },
  
  /**
   * 设置默认天气数据
   */
  setDefaultWeatherData: function() {
    this.setData({
      'weatherData.temp': '25',
      'weatherData.text': '晴',
      'weatherData.icon': '100',
      'weatherData.tDesignIcon': 'sunny',
      'weatherData.location': '未知位置',
      'weatherData.tips': '今天天气不错，适合发布新的供应信息~'
    });
  },

  /**
   * 获取天气提示语
   */
  getWeatherTips: function(weatherText) {
    const tips = {
      '晴': '阳光明媚，适合拍摄供应照片~',
      '多云': '光线柔和，拍照效果更佳！',
      '阴': '光线均匀，适合拍摄植物细节~',
      '雨': '雨后植物更显生机，记得做好防水措施~',
      '雪': '雪景如画，给您的供应添加独特魅力~',
      '雾': '注意保持植物湿润，雾天拍照需注意曝光~'
    };
    
    // 根据天气文本返回对应提示，如果没有匹配则返回默认提示
    for (const key in tips) {
      if (weatherText.includes(key)) {
        return tips[key];
      }
    }
    
    return '今天天气不错，适合发布新的供应信息~';
  },

  /**
   * 启动天气图标动画
   */
  startWeatherAnimation: function() {
    // 每3秒切换一次动画状态，营造呼吸效果
    setInterval(() => {
      this.setData({
        weatherAnimation: !this.data.weatherAnimation
      });
    }, 3000);
  },

  /**
   * 点击头像处理
   */
  onTapAvatar: function() {
    // 如果未登录，跳转到用户页面进行登录
    if (!this.data.isLogined) {
      wx.switchTab({
        url: '/pages/user/user'
      });
      return;
    }
    
    // 如果已登录，跳转到用户页面
    wx.switchTab({
      url: '/pages/user/user'
    });
  },
}) 